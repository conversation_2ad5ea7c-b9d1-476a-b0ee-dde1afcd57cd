// The module 'vscode' contains the VS Code extensibility API
// Import the module and reference it with the alias vscode in your code below
import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { getConfig, setConfig } from './utils/configManager';
import { SidebarProvider } from './views/sidebarProvider';
import { executeJar } from './utils/jarExecutor';
import { getTestableMethods, getMethodCallGraph, getMethodDetails } from './utils/cypherQuery';
import axios from 'axios';

// This method is called when your extension is activated
// Your extension is activated the very first time the command is executed
export function activate(context: vscode.ExtensionContext) {

	// Use the console to output diagnostic information (console.log) and errors (console.error)
	// This line of code will only be executed once when your extension is activated
	console.log('Congratulations, your extension "testagent" is now active!');

	// The command has been defined in the package.json file
	// Now provide the implementation of the command with registerCommand
	// The commandId parameter must match the command field in package.json
	const disposable = vscode.commands.registerCommand('testagent.helloWorld', () => {
		// The code you place here will be executed every time your command is executed
		// Display a message box to the user
		vscode.window.showInformationMessage('Hello World from TestAgent!');
	});

	context.subscriptions.push(disposable);

	let graphPanel: vscode.WebviewPanel | undefined;
	let settingsPanel: vscode.WebviewPanel | undefined;

	const showGraphCommand = vscode.commands.registerCommand('testagent.showGraph', (cypherQuery?: string) => {
		if (graphPanel) {
			// 如果面板已存在，则将其显示
			graphPanel.reveal(vscode.ViewColumn.One);

			// 如果有传入的 Cypher 查询，则发送到 Webview
			if (cypherQuery) {
				graphPanel.webview.postMessage({
					command: 'updateCypherQuery',
					cypherQuery,
				});
			}
			return;
		}

		graphPanel = vscode.window.createWebviewPanel(
			'knowledgeGraph', // 标识符
			'Knowledge Graph', // 面板标题
			vscode.ViewColumn.One, // 显示在编辑器的第一列
			{
				enableScripts: true, // 允许 Webview 执行脚本
			}
		);

		// 读取 graph.html 文件内容
		const graphHtmlPath = path.join(context.extensionPath, 'src', 'webview', 'graph.html');
		const graphHtmlContent = fs.readFileSync(graphHtmlPath, 'utf8');
		graphPanel.webview.html = graphHtmlContent;

		// 发送当前配置到 Webview
		const currentConfig = getConfig();
		graphPanel.webview.postMessage({
			command: 'initializeGraph',
			config: currentConfig,
		});

		// 监听面板关闭事件
		graphPanel.onDidDispose(() => {
			graphPanel = undefined;
		});

		// 监听面板状态变化事件
		graphPanel.onDidChangeViewState(() => {
			if (graphPanel?.visible) {
				// 当面板变为活动状态时重新发送数据
				graphPanel.webview.postMessage({
					command: 'initializeGraph',
					config: getConfig(),
				});
			}
		});
	});

	context.subscriptions.push(showGraphCommand);

	const configureCommand = vscode.commands.registerCommand('testagent.configure', () => {
		if (settingsPanel) {
			// 如果面板已存在，则将其显示
			settingsPanel.reveal(vscode.ViewColumn.One);
			return;
		}

		settingsPanel = vscode.window.createWebviewPanel(
			'settings',
			'Configuration Settings',
			vscode.ViewColumn.One,
			{
				enableScripts: true,
			}
		);

		const settingsHtmlPath = path.join(context.extensionPath, 'src', 'webview', 'setting.html');
		settingsPanel.webview.html = fs.readFileSync(settingsHtmlPath, 'utf8');

		// 发送当前配置到 Webview
		const currentConfig = getConfig();
		settingsPanel.webview.postMessage(currentConfig);

		settingsPanel.webview.onDidReceiveMessage(
			(message) => {
				if (message.command === 'saveSettings') {
					setConfig(message.settings);
					vscode.window.showInformationMessage('Settings saved successfully!');
				}
			},
			undefined,
			context.subscriptions
		);

		// 监听面板关闭事件
		settingsPanel.onDidDispose(() => {
			settingsPanel = undefined;
		});

		// 监听面板状态变化事件
		settingsPanel.onDidChangeViewState(() => {
			if (settingsPanel?.visible) {
				// 当面板变为活动状态时重新发送数据
				const currentConfig = getConfig();
				settingsPanel.webview.postMessage(currentConfig);
			}
		});
	});

	context.subscriptions.push(configureCommand);

	const generateGraphCommand = vscode.commands.registerCommand('testagent.generateGraph', async () => {
		const projectPath = await vscode.window.showOpenDialog({
			canSelectFolders: true,
			canSelectFiles: false,
			canSelectMany: false,
			openLabel: 'Select Project Folder',
		});
	
		if (!projectPath || projectPath.length === 0) {
			vscode.window.showErrorMessage('No project folder selected.');
			return;
		}
	
		executeJar(projectPath[0].fsPath);
	});
	
	context.subscriptions.push(generateGraphCommand);

	const getTestableMethodsCommand = vscode.commands.registerCommand('testagent.getTestableMethods', async () => {
		const methods = await getTestableMethods();
		if (methods.length === 0) {
			vscode.window.showInformationMessage('No testable methods found.');
			return;
		}
	
		const selectedMethod = await vscode.window.showQuickPick(
			methods.map(method => ({ label: method.signature, description: `Node ID: ${method.nodeId}`, nodeId: method.nodeId })),
			{ placeHolder: 'Select a testable method' }
		);
	
		if (selectedMethod) {
			const cypherQuery = await getMethodCallGraph(selectedMethod.nodeId);
			if (cypherQuery) {
				vscode.commands.executeCommand('testagent.showGraph', cypherQuery);
			} else {
				vscode.window.showInformationMessage('No call graph found for the selected method.');
			}
		}
	});
	
	context.subscriptions.push(getTestableMethodsCommand);

	const showMethodCallGraphCommand = vscode.commands.registerCommand(
		'testagent.showMethodCallGraph',
		async (nodeId: number) => {
		  const cypherQuery = await getMethodCallGraph(nodeId);
		  if (cypherQuery) {
			vscode.commands.executeCommand('testagent.showGraph', cypherQuery);
		  } else {
			vscode.window.showInformationMessage('No call graph found for the selected method.');
		  }
		}
	  );
	
	  context.subscriptions.push(showMethodCallGraphCommand);

	const showMethodDetailsCommand = vscode.commands.registerCommand(
		'testagent.showMethodDetails',
		async (nodeId: number, signature: string) => {
			const cypherQuery = `MATCH (m:Method)-[r]->(n) WHERE id(m) = ${nodeId} RETURN m, r, n`;
			if (graphPanel) {
				// 发送消息到 Webview，更新图谱和 Cypher 查询栏
				graphPanel.webview.postMessage({
					command: 'showMethodDetails',
					cypherQuery,
					signature,
				});
			} else {
				vscode.window.showErrorMessage('Knowledge Graph panel is not open.');
			}
		}
	);
	
	context.subscriptions.push(showMethodDetailsCommand);

	const generateTestCasesCommand = vscode.commands.registerCommand(
		'testagent.generateTestCases',
		async (itemOrItems: any, selectedItems?: any[]) => {
		  vscode.window.showInformationMessage('Generate Test Cases command triggered!');
		  
		  // 支持多选
		  const items = Array.isArray(selectedItems) && selectedItems.length > 0 ? selectedItems : [itemOrItems];
		  const config = getConfig();
		  const outputChannel = vscode.window.createOutputChannel('Test Case Generation');
		  outputChannel.show(true);
	  
		  for (const item of items) {
			 // 直接从 item 获取 nodeId
			const nodeId = typeof item.nodeId?.toNumber === 'function' ? item.nodeId.toNumber() : item.nodeId;
			const basePath = vscode.workspace.workspaceFolders?.[0].uri.fsPath;
			const signature = item.label;
			if (!nodeId || typeof nodeId !== 'number') {
			  outputChannel.appendLine(`[${signature}] Invalid node ID.`);
			  continue;
			}
	  
			outputChannel.appendLine(`\n[${signature}] Generating test case...`);
	  
			try {
			  // 调用 FastAPI SSE 流式接口
			  const url = 'http://localhost:8000/generate_test_case_stream';
			  const payload = {
				method_id: nodeId,
				url: config.neo4j.serverUrl,
				username: config.neo4j.serverUser,
				password: config.neo4j.serverPassword,
				limit: 50,
				base_path: basePath,
			  };
	  
			  const response = await axios.post(url, payload, {
				responseType: 'stream'
			  });
	  
			  // 监听流式输出
			  response.data.on('data', (chunk: Buffer) => {
				const lines = chunk.toString().split('\n');
				for (const line of lines) {
				  if (!line.trim()) continue;
				  if (line.startsWith('data:')) {
					const data = line.replace(/^data:\s*/, '');
					outputChannel.appendLine(data);
				  }
				}
			  });
	  
			  await new Promise((resolve, reject) => {
				response.data.on('end', resolve);
				response.data.on('error', reject);
			  });
	  
			  outputChannel.appendLine(`[${signature}] Test case generation completed.\n`);
			} catch (err) {
			  outputChannel.appendLine(`[${signature}] Error: ${err}`);
			}
		  }
		}
	  );
	
	  context.subscriptions.push(generateTestCasesCommand);

	// 跳转到被测函数
	const jumpToTestableMethodCommand = vscode.commands.registerCommand(
		'testagent.jumpToTestableMethod',
		async (item: any) => {
		  const nodeId = typeof item.nodeId?.toNumber === 'function' ? item.nodeId.toNumber() : item.nodeId; // 确保 nodeId 是 number 类型
		  let absolutePath = item.absolutePath;
		  let startLine = typeof item.startLine?.toNumber === 'function' ? item.startLine.toNumber() : item.startLine; // 确保 startLine 是 number 类型
	
		  console.log('Jump to Testable Method properties:', { nodeId, absolutePath, startLine }); // 调试日志
	
		  if (!nodeId) {
			vscode.window.showErrorMessage('Invalid method data: Missing node ID.');
			return;
		  }
	
		  if (!absolutePath || startLine === undefined) {
			// 如果数据不完整，从数据库查询详细信息
			const methodDetails = await getMethodDetails(nodeId);
			if (!methodDetails) {
			  return;
			}
	
			absolutePath = methodDetails.absolutePath;
			startLine = methodDetails.startLine;
		  }
	
		  if (!absolutePath || startLine === undefined) {
			vscode.window.showErrorMessage('Invalid method data: Missing absolute path or start line.');
			return;
		  }
	
		  const document = await vscode.workspace.openTextDocument(absolutePath);
		  const editor = await vscode.window.showTextDocument(document);
		  const position = new vscode.Position(startLine - 1, 0); // 行号从 0 开始
		  editor.selection = new vscode.Selection(position, position);
		  editor.revealRange(new vscode.Range(position, position), vscode.TextEditorRevealType.InCenter);
		}
	  );
	
	  context.subscriptions.push(jumpToTestableMethodCommand);
	
	  // 定位到 Testable Method
	  const locateInSidebarCommand = vscode.commands.registerCommand(
		'testagent.locateInSidebar',
		async () => {
		  const editor = vscode.window.activeTextEditor;
		  if (!editor) {
			vscode.window.showErrorMessage('No active editor found.');
			return;
		  }
	
		  const filePath = editor.document.uri.fsPath;
		  const currentLine = editor.selection.active.line + 1; // 行号从 1 开始
	
		  const methods = await getTestableMethods();
		  const matchedMethod = methods.find(
			(method) =>
			  method.absolutePath === filePath &&
			  method.startLine <= currentLine &&
			  method.endLine >= currentLine
		  );
	
		  if (matchedMethod) {
			// 展开并选中侧边栏节点
			const item = new (require('./views/sidebarProvider').SidebarItem)(
				matchedMethod.signature,
				vscode.TreeItemCollapsibleState.None,
				undefined,
				undefined,
				'methodNode',
				matchedMethod.nodeId,
				matchedMethod.absolutePath,
				matchedMethod.startLine
			);
			sidebarView.reveal(item, { select: true, focus: true, expand: true });
			vscode.window.showInformationMessage(`Located method: ${matchedMethod.signature}`);
		  } else {
			vscode.window.showErrorMessage('No matching Testable Method found.');
		  }
		}
	  );
	
	  context.subscriptions.push(locateInSidebarCommand);

	const showInKnowledgeGraphCommand = vscode.commands.registerCommand(
		'testagent.showInKnowledgeGraph',
		async (item: any) => {
		  const nodeId = typeof item.nodeId?.toNumber === 'function' ? item.nodeId.toNumber() : item.nodeId;
		  if (!nodeId) {
			vscode.window.showErrorMessage('Invalid method data: Missing node ID.');
			return;
		  }
		  const cypherQuery = await getMethodCallGraph(nodeId);
		  if (cypherQuery) {
			vscode.commands.executeCommand('testagent.showGraph', cypherQuery);
		  } else {
			vscode.window.showInformationMessage('No call graph found for the selected method.');
		  }
		}
	  );
	
	  context.subscriptions.push(showInKnowledgeGraphCommand);

	// 注册 TreeView 类型的侧边栏
	const sidebarProvider = new SidebarProvider(context.extensionUri); // 传递 context.extensionUri
	const sidebarView = vscode.window.createTreeView('testagent.sidebar', {
		treeDataProvider: sidebarProvider,
		canSelectMany: true // 关键：支持多选和右键菜单
	});

	context.subscriptions.push(
		vscode.commands.registerCommand('testagent.refreshSidebar', () => {
			sidebarProvider.refresh();
		})
	);

	const refreshTestableMethodsCommand = vscode.commands.registerCommand(
		'testagent.refreshTestableMethods',
		async () => {
		  sidebarProvider.refresh();
		  vscode.window.showInformationMessage('Testable Methods refreshed.');
		}
	  );
	  context.subscriptions.push(refreshTestableMethodsCommand);

	function findReportJsonByNodeId(reportDir: string, nodeId: number): string | undefined {
		if (!fs.existsSync(reportDir)) return undefined;
		const files = fs.readdirSync(reportDir);
		return files.find(f => f.endsWith('.json') && f.split('_')[0] === String(nodeId));
	  }
	  
	  function extractClassName(testCase: string): string | undefined {
		const match = testCase.match(/class\s+([A-Za-z0-9_]+)/);
		return match ? match[1] : undefined;
	  }
	  
	  function buildTestCaseComment(tc: any): string {
		const lines = [];
		lines.push('/**');
		lines.push(` * test_result: ${tc.test_result}`);
		lines.push(` * find_bug: ${tc.find_bug}`);
		if (tc.bug_report) lines.push(` * bug_report: ${tc.bug_report}`);
		if (tc.test_point) {
		  if (tc.test_point.Test_Purpose) lines.push(` * Test_Purpose: ${tc.test_point.Test_Purpose}`);
		  if (tc.test_point.Input_Type) lines.push(` * Input_Type: ${tc.test_point.Input_Type}`);
		  if (tc.test_point.Output_Type) lines.push(` * Output_Type: ${tc.test_point.Output_Type}`);
		}
		if (tc.coverage_report) {
		  lines.push(` * coverage_report:`);
		  lines.push(` *   result: ${tc.coverage_report.result}`);
		  if (tc.coverage_report.output) {
			lines.push(` *   function_name: ${tc.coverage_report.output.function_name}`);
			lines.push(` *   line_coverage: ${tc.coverage_report.output.line_coverage}`);
			lines.push(` *   branch_coverage: ${tc.coverage_report.output.branch_coverage}`);
			lines.push(` *   covered_lines: ${JSON.stringify(tc.coverage_report.output.covered_lines)}`);
			lines.push(` *   missed_lines: ${JSON.stringify(tc.coverage_report.output.missed_lines)}`);
		  }
		}
		if (tc.mutation_report) {
		  lines.push(` * mutation_report:`);
		  lines.push(` *   result: ${tc.mutation_report.result}`);
		  const mutationOutput = tc.mutation_report.output;
		  if (typeof mutationOutput === 'object' && mutationOutput !== null) {
			if ('mutation_score' in mutationOutput) {
			  lines.push(` *   mutation_score: ${mutationOutput.mutation_score}`);
			}
			if (Array.isArray(mutationOutput.filtered_mutations)) {
			  lines.push(` *   filtered_mutations:`);
			  for (const m of mutationOutput.filtered_mutations) {
				lines.push(` *     - Class: ${m.Class}`);
				lines.push(` *       Method: ${m.Method}`);
				lines.push(` *       Line: ${m.Line}`);
				lines.push(` *       Mutator: ${m.Mutator}`);
				lines.push(` *       Description: ${m.Description}`);
				lines.push(` *       Status: ${m.Status}`);
				lines.push(` *       Tests Run: ${m["Tests Run"]}`);
				lines.push(` *       Killing Test: ${m["Killing Test"]}`);
			  }
			} else {
			  lines.push(` *   output: ${JSON.stringify(mutationOutput)}`);
			}
		  } else {
			lines.push(` *   output: ${mutationOutput}`);
		  }
		}
		lines.push(' */');
		return lines.join('\n');
	  }
	  
	  async function injectTestCaseForMethod(method: any, workspaceRoot: string, outputChannel: vscode.OutputChannel) {
		// 1. 计算 inject_dir
		if (!method.absolutePath) {
		  outputChannel.appendLine(`[${method.signature}] No absolutePath found.`);
		  return;
		}
		const injectPath = method.absolutePath.replace(/src[\/\\]main/, 'src/test');
		const injectDir = injectPath.substring(0, injectPath.lastIndexOf(path.sep));
	  
		// 2. 查找 result/report 目录
		const resultDir = path.join(workspaceRoot, 'result', 'report');
		const jsonFile = findReportJsonByNodeId(resultDir, method.nodeId);
		if (!jsonFile) {
		  outputChannel.appendLine(`[${method.signature}] No report json found for nodeId ${method.nodeId}.`);
		  return;
		}
		const jsonPath = path.join(resultDir, jsonFile);
		let jsonData;
		try {
		  jsonData = JSON.parse(fs.readFileSync(jsonPath, 'utf8'));
		} catch (e) {
		  outputChannel.appendLine(`[${method.signature}] Failed to parse json: ${e}`);
		  return;
		}
		// 3. 计算最终注入目录
		const packageName = jsonData.package_name;
		if (!packageName) {
		  outputChannel.appendLine(`[${method.signature}] No package_name in json.`);
		  return;
		}
		const packagePath = packageName.replace(/\./g, path.sep);
		const finalDir = path.join(injectDir, packagePath);
		if (!fs.existsSync(finalDir)) {
		  fs.mkdirSync(finalDir, { recursive: true });
		}
		// 4. 遍历 test_cases
		let injectedCount = 0;
		for (const tc of jsonData.test_cases || []) {
		  // 满足 Success 或 (Execute Error 且 find_bug 为 true)
		  const isInjectable =
			(tc.test_result === 'Success') ||
			(tc.test_result === 'Execute Error' && tc.find_bug === true);
		  if (!isInjectable || !tc.test_case) continue;
		  const className = extractClassName(tc.test_case);
		  if (!className) {
			outputChannel.appendLine(`[${method.signature}] Cannot extract class name from test case.`);
			continue;
		  }
		  // 注释内容
		  const comment = buildTestCaseComment(tc);
		  // 注入内容：注释 + 换行 + 原始 test_case
		  const filePath = path.join(finalDir, `${className}.java`);
		  fs.writeFileSync(filePath, `${comment}\n${tc.test_case}`, 'utf8');
		  injectedCount++;
		}
		outputChannel.appendLine(`[${method.signature}] Injected ${injectedCount} test case(s) to ${finalDir}`);
	  }
	  
	  const injectTestCaseCommand = vscode.commands.registerCommand(
		'testagent.injectTestCase',
		async (item: any) => {
		  const workspaceFolders = vscode.workspace.workspaceFolders;
		  if (!workspaceFolders || workspaceFolders.length === 0) {
			vscode.window.showErrorMessage('No workspace folder found.');
			return;
		  }
		  const workspaceRoot = workspaceFolders[0].uri.fsPath;
		  const outputChannel = vscode.window.createOutputChannel('Test Case Injection');
		  outputChannel.show(true);
		  await injectTestCaseForMethod(item, workspaceRoot, outputChannel);
		}
	  );
	  context.subscriptions.push(injectTestCaseCommand);
	  
	  const injectAllTestCasesCommand = vscode.commands.registerCommand(
		'testagent.injectAllTestCases',
		async () => {
		  const workspaceFolders = vscode.workspace.workspaceFolders;
		  if (!workspaceFolders || workspaceFolders.length === 0) {
			vscode.window.showErrorMessage('No workspace folder found.');
			return;
		  }
		  const workspaceRoot = workspaceFolders[0].uri.fsPath;
		  const outputChannel = vscode.window.createOutputChannel('Test Case Injection');
		  outputChannel.show(true);
	  
		  const methods = await getTestableMethods();
		  // 只处理有测试用例标识的
		  // 修改为带 .js 扩展名以兼容 node16/nodenext
		  const { hasDirectTestClazz } = await import('./utils/cypherQuery.js');
		  const testFlags = await Promise.all(methods.map(m => hasDirectTestClazz(m.nodeId)));
		  let total = 0;
		  for (let i = 0; i < methods.length; ++i) {
			if (testFlags[i]) {
			  await injectTestCaseForMethod(methods[i], workspaceRoot, outputChannel);
			  total++;
			}
		  }
		  outputChannel.appendLine(`\n[Inject All] Injected test cases for ${total} methods.`);
		}
	  );
	  context.subscriptions.push(injectAllTestCasesCommand);
}

// This method is called when your extension is deactivated
export function deactivate() {}

