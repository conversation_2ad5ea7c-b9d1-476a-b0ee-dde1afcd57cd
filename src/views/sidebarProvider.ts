import * as vscode from 'vscode';
import * as path from 'path';
import { getTestableMethods, hasDirectTestClazz } from '../utils/cypherQuery';

export class SidebarProvider implements vscode.TreeDataProvider<SidebarItem> {
  private _onDidChangeTreeData: vscode.EventEmitter<SidebarItem | undefined | void> =
    new vscode.EventEmitter<SidebarItem | undefined | void>();
  readonly onDidChangeTreeData: vscode.Event<SidebarItem | undefined | void> =
    this._onDidChangeTreeData.event;

  private testableMethods: SidebarItem[] = []; // 存储被测函数的树节点

  constructor(private readonly extensionUri: vscode.Uri) {}

  getTreeItem(element: SidebarItem): vscode.TreeItem {
    return element;
  }

  async getChildren(element?: SidebarItem): Promise<SidebarItem[]> {
    if (!element) {
      // 根节点：固定选项 + 方法分组入口
      return [
        new SidebarItem(
          'Configure Settings',
          vscode.TreeItemCollapsibleState.None,
          {
            command: 'testagent.configure',
            title: 'Configure Settings',
          },
          this.getIconPath('settings')
        ),
        new SidebarItem(
          'Show Knowledge Graph',
          vscode.TreeItemCollapsibleState.None,
          {
            command: 'testagent.showGraph',
            title: 'Show Knowledge Graph',
          },
          this.getIconPath('graph')
        ),
        new SidebarItem(
          'Generate Knowledge Graph',
          vscode.TreeItemCollapsibleState.None,
          {
            command: 'testagent.generateGraph',
            title: 'Generate Knowledge Graph',
          },
          this.getIconPath('generate')
        ),
        new SidebarItem(
          'Testable Methods',
          vscode.TreeItemCollapsibleState.Collapsed,
          undefined,
          this.getIconPath('testableMethods'),
          'testableMethodsRoot' // 设置 contextValue
        ),
      ];
    }

    if (element.label === 'Testable Methods') {
      // 子节点：被测方法列表
      if (this.testableMethods.length === 0) {
        vscode.window.showInformationMessage('Fetching testable methods...');
        const methods = await getTestableMethods();
        console.log('Fetched testable methods:', methods); // 调试日志

        // 并发查询每个方法是否有测试类
        const testFlags = await Promise.all(
          methods.map(m => hasDirectTestClazz(m.nodeId))
        );

        this.testableMethods = methods.map(
          (method, idx) =>
            new SidebarItem(
              method.signature,
              vscode.TreeItemCollapsibleState.None,
              undefined, // 移除 command 属性
              testFlags[idx]
                ? this.getIconPath('method-tested') // 有测试类则用特殊图标
                : this.getIconPath('method'),       // 否则用默认图标
              'methodNode', // contextValue 用于右键菜单匹配
              method.nodeId,
              method.absolutePath,
              method.startLine
            )
        );
      }
      return this.testableMethods;
    }

    return [];
  }

  getParent(element: SidebarItem): SidebarItem | null {
    // 只有 Testable Methods 的子节点才有父节点
    if (element.contextValue === 'methodNode') {
      return new SidebarItem(
        'Testable Methods',
        vscode.TreeItemCollapsibleState.Collapsed,
        undefined,
        this.getIconPath('testableMethods')
      );
    }
    return null;
  }

  refresh(): void {
    this.testableMethods = [];
    this._onDidChangeTreeData.fire();
  }

  private getIconPath(iconName: string): { light: vscode.Uri; dark: vscode.Uri } {
    return {
      light: vscode.Uri.joinPath(this.extensionUri, 'resources', 'light', `${iconName}.svg`),
      dark: vscode.Uri.joinPath(this.extensionUri, 'resources', 'dark', `${iconName}.svg`),
    };
  }
}

export class SidebarItem extends vscode.TreeItem {
  constructor(
    public readonly label: string,
    public readonly collapsibleState: vscode.TreeItemCollapsibleState,
    public readonly command?: vscode.Command,
    public readonly iconPath?: { light: vscode.Uri; dark: vscode.Uri },
    public readonly contextValue?: string,
    public readonly nodeId?: number,
    public readonly absolutePath?: string,
    public readonly startLine?: number
  ) {
    super(label, collapsibleState);
    this.command = command;
    this.iconPath = iconPath;
    this.contextValue = contextValue;
    this.nodeId = nodeId;
    this.absolutePath = absolutePath;
    this.startLine = startLine;
  }
}