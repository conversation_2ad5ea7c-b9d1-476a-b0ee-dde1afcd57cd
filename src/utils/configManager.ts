import * as fs from 'fs';
import * as path from 'path';

const configPath = path.join(__dirname, '../../config/userConfig.json');

export function getConfig(): any {
  if (!fs.existsSync(configPath)) {
    throw new Error('Configuration file not found.');
  }
  const configData = fs.readFileSync(configPath, 'utf8');
  return JSON.parse(configData);
}

export function setConfig(newConfig: any): void {
  const configData = JSON.stringify(newConfig, null, 2);
  fs.writeFileSync(configPath, configData, 'utf8');
}
