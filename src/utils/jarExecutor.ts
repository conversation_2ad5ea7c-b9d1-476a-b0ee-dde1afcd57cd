import * as child_process from 'child_process';
import * as vscode from 'vscode';
import { getConfig } from './configManager';
import * as path from 'path';
import * as fs from 'fs';

export function executeJar(projectPath: string): void {
  const config = getConfig();

  const jarPath = config.testAgent.ckgConstructionJarPath;
  const neo4jUrl = config.neo4j.serverUrl;
  const neo4jUser = config.neo4j.serverUser;
  const neo4jPassword = config.neo4j.serverPassword;

  if (!jarPath || !neo4jUrl || !neo4jUser || !neo4jPassword) {
    vscode.window.showErrorMessage('Please configure the JAR path and Neo4j connection settings in the settings.');
    return;
  }

  // 设置日志目录为插件工作目录下的 logs 文件夹
  const extensionLogDir = path.join(__dirname, '../../logs');
  if (!fs.existsSync(extensionLogDir)) {
    fs.mkdirSync(extensionLogDir, { recursive: true });
  }

  const logPath = path.join(extensionLogDir, 'ckg.log'); // 指定日志文件路径
  const command = `java`;
  const args = [
    `-Dlog.path=${logPath}`,
    `-jar`,
    jarPath,
    `-p`,
    projectPath,
    `-u`,
    neo4jUrl,
    `-n`,
    neo4jUser,
    `-w`,
    neo4jPassword,
  ];

  try {
    vscode.window.showInformationMessage('Generating knowledge graph...');

    // 创建输出通道
    const outputChannel = vscode.window.createOutputChannel('Knowledge Graph Generation');
    outputChannel.show(true);

    const process = child_process.spawn(command, args);

    // 监听标准输出
    process.stdout.on('data', (data) => {
      outputChannel.append(data.toString());
    });

    // 监听标准错误
    process.stderr.on('data', (data) => {
      outputChannel.append(`[ERROR] ${data.toString()}`);
    });

    // 监听进程退出
    process.on('close', (code) => {
      if (code === 0) {
        vscode.window.showInformationMessage('Knowledge graph generated successfully!');
      } else {
        vscode.window.showErrorMessage(`Knowledge graph generation failed with exit code ${code}.`);
      }
      outputChannel.appendLine(`Process exited with code ${code}`);
    });
  } catch (err) {
    vscode.window.showErrorMessage(`Failed to execute JAR: ${err}`);
  }
}
