<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Knowledge Graph</title>
  <style>
    html, body {
      height: 100%;
      margin: 0;
      display: flex;
      flex-direction: column;
    }
  
    #graph-container {
      flex: 1; /* 自动占据剩余空间 */
      min-height: 400px; /* 给个最小高度，防止太小 */
      border: 1px solid lightgray;
    }
  
    #controls {
      padding: 10px;
      box-sizing: border-box;
    }
  
    textarea {
      width: 100%;
      margin-top: 10px;
      font-size: 14px;
    }
  
    button {
      margin-top: 10px;
      margin-right: 10px;
      padding: 5px 10px;
      font-size: 14px;
    }
  </style>
  <script src="https://cdn.jsdelivr.net/npm/neovis.js@2.0.2/dist/neovis.js"></script>
</head>
<body>
  <div id="graph-container"></div>
  <div id="controls">
    <label for="cypher-query">Cypher Query:</label>
    <textarea id="cypher-query" rows="4" placeholder="Enter Cypher query here...">MATCH (n)-[r]->(m) RETURN n, r, m LIMIT 50</textarea>
    <br>
    <button id="reload">Submit Query</button>
    <button id="stabilize">Stabilize Graph</button>
  </div>

  <script>
    let viz;
    const vscode = acquireVsCodeApi();

    // 初始化图谱
    function initializeGraph(config) {
      const neo4jConfig = {
        containerId: "graph-container",
        neo4j: {
          serverUrl: config.neo4j.serverUrl,
          serverUser: config.neo4j.serverUser,
          serverPassword: config.neo4j.serverPassword,
        },
        labels: {
          Clazz: {
            [NeoVis.NEOVIS_ADVANCED_CONFIG]: {
                static: {
                    shape: "dot"
                },
                function: {
                    title: (node) => `Type: Class
                    Name: ${node.properties.name}
                    Full Qualified Name: ${node.properties.full_qualified_name}
                    `,
                    color: (node) => "#FF5733",  // 橙色
                    size: () => 30,  // 固定为 50
                    label: (node) => node.properties.name,
              },
            },
          },
          Method: {
            caption: "name",
            [NeoVis.NEOVIS_ADVANCED_CONFIG]: {
                static: {
                    shape: "dot"
                },
                function: {
                    title: (node) => `Type: Method(${node.properties.type})
                    Name: ${node.properties.name}
                    Full Qualified Name: ${node.properties.full_qualified_name}
                    Signature: ${node.properties.signature}
                    `,
                    color: (node) => "#3357FF",  // 蓝色
                    size: () => 25, 
                    label: (node) => node.properties.name,
                },
            },
          },
          Field: {
            caption: "name",
            size: "size",
            [NeoVis.NEOVIS_ADVANCED_CONFIG]: {
                static: {
                    shape: "dot"
                },
                function: {
                    title: (node) => `Type: Field
                    Name: ${node.properties.name}
                    Full Qualified Name: ${node.properties.full_qualified_name}
                    `,
                    color: (node) => "#33FF57",  // 绿色
                    size: () => 20, 
                    label: (node) => node.properties.name,
                },
            },
          },
          TestClazz: {
            caption: "name",
            size: "size",
            [NeoVis.NEOVIS_ADVANCED_CONFIG]: {
                static: {
                    shape: "dot"
                },
                function: {
                    title: (node) => `Type: TestClass
                    Name: ${node.properties.name}
                    Tested Class: ${node.properties.tested_clazz_FQname}
                    `,
                    color: (node) => "#FF33A1",  // 粉色
                    size: () => 25, 
                    label: (node) => node.properties.name,
                },
            },
          },
        },
        relationships: {
            CONTAIN: {
                caption: false,
                [NeoVis.NEOVIS_ADVANCED_CONFIG]: {
                static: { arrows: "to" },
                function: {
                    title: (rel) => `${rel.type}`
                }
                }
            },
            DEPENDENCY: {
                caption: false,
                [NeoVis.NEOVIS_ADVANCED_CONFIG]: {
                static: { arrows: "to" },
                function: {
                    title: (rel) => `${rel.type}`
                }
                }
            },
            EXTEND: {
                caption: false,
                [NeoVis.NEOVIS_ADVANCED_CONFIG]: {
                static: { arrows: "to" },
                function: {
                    title: (rel) => `${rel.type}`
                }
                }
            },
            IMPLEMENT: {
                caption: false,
                [NeoVis.NEOVIS_ADVANCED_CONFIG]: {
                static: { arrows: "to" },
                function: {
                    title: (rel) => `${rel.type}`
                }
                }
            },
            INVOKE: {
                caption: false,
                [NeoVis.NEOVIS_ADVANCED_CONFIG]: {
                static: { arrows: "to" },
                function: {
                    title: (rel) => `${rel.type}`
                }
                }
            }
            },
        initialCypher: "MATCH (n)-[r]->(m) RETURN n, r, m LIMIT 50",
      };

      viz = new NeoVis.default(neo4jConfig);

      // 注册事件监听器
      viz.registerOnEvent("completed", () => {
        console.log("图谱渲染完成！");
      });

      viz.registerOnEvent("error", (err) => {
        console.error("图谱渲染错误：", err);
      });

      viz.render();
    }

    // 动态查询
    function submitQuery() {
      const query = document.getElementById("cypher-query").value;
      if (query && query.trim().length > 0) {
        viz.renderWithCypher(query);
      } else {
        console.error("Invalid Cypher query");
      }
    }

    // 稳定化图谱
    function stabilizeGraph() {
      viz.stabilize();
    }

    // 绑定事件
    function bindEvents() {
      document.getElementById("reload").addEventListener("click", submitQuery);
      document.getElementById("stabilize").addEventListener("click", stabilizeGraph);
    }

    // 接收来自扩展的消息
    window.addEventListener('message', (event) => {
      const message = event.data;

      if (message.command === 'initializeGraph') {
        initializeGraph(message.config);
      } else if (message.command === 'updateCypherQuery') {
        // 更新 Cypher 查询栏
        document.getElementById("cypher-query").value = message.cypherQuery;

        // 在图谱中执行查询
        viz.renderWithCypher(message.cypherQuery);
      }
    });

    bindEvents();
  </script>
</body>
</html>
