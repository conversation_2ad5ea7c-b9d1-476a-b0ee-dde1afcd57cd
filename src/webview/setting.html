<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Settings</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
    }
    label {
      display: block;
      margin-top: 10px;
    }
    input, textarea {
      width: 100%;
      margin-top: 5px;
    }
    button {
      margin-top: 20px;
    }
  </style>
</head>
<body>
  <h1>Configuration Settings</h1>
  <form id="settings-form">
    <label for="serverUrl">Neo4j Server URL</label>
    <input type="text" id="serverUrl" name="serverUrl">

    <label for="serverUser">Neo4j Server User</label>
    <input type="text" id="serverUser" name="serverUser">

    <label for="serverPassword">Neo4j Server Password</label>
    <input type="password" id="serverPassword" name="serverPassword">

    <label for="recursionLimit">Agent Recursion Limit</label>
    <input type="number" id="recursionLimit" name="recursionLimit">

    <label for="openaiApiBase">OpenAI API Base</label>
    <input type="text" id="openaiApiBase" name="openaiApiBase">

    <label for="openaiApiKey">OpenAI API Key</label>
    <input type="password" id="openaiApiKey" name="openaiApiKey">

    <label for="enableNativeFunctionCall">Enable Native Function Call</label>
    <input type="checkbox" id="enableNativeFunctionCall" name="enableNativeFunctionCall">

    <label for="limitRetrieveTestCase">Limit Retrieve Test Case</label>
    <input type="checkbox" id="limitRetrieveTestCase" name="limitRetrieveTestCase">

    <label for="ckgConstructionJarPath">CKG Construction JAR Path</label>
    <input type="text" id="ckgConstructionJarPath" name="ckgConstructionJarPath">

    <button type="button" id="save-settings">Save Settings</button>
  </form>

  <script>
    const vscode = acquireVsCodeApi();

    // 加载当前配置并填充表单
    window.addEventListener('message', (event) => {
      const config = event.data;
      document.getElementById('serverUrl').value = config.neo4j.serverUrl || '';
      document.getElementById('serverUser').value = config.neo4j.serverUser || '';
      document.getElementById('serverPassword').value = config.neo4j.serverPassword || '';
      document.getElementById('recursionLimit').value = config.testAgent.recursionLimit || 50;
      document.getElementById('openaiApiBase').value = config.testAgent.openaiApiBase || '';
      document.getElementById('openaiApiKey').value = config.testAgent.openaiApiKey || '';
      document.getElementById('enableNativeFunctionCall').checked = config.testAgent.enableNativeFunctionCall || false;
      document.getElementById('limitRetrieveTestCase').checked = config.testAgent.limitRetrieveTestCase || false;
      document.getElementById('ckgConstructionJarPath').value = config.testAgent.ckgConstructionJarPath || '';
    });

    // 保存配置
    document.getElementById('save-settings').addEventListener('click', () => {
      const settings = {
        neo4j: {
          serverUrl: document.getElementById('serverUrl').value,
          serverUser: document.getElementById('serverUser').value,
          serverPassword: document.getElementById('serverPassword').value,
        },
        testAgent: {
          recursionLimit: parseInt(document.getElementById('recursionLimit').value, 10),
          openaiApiBase: document.getElementById('openaiApiBase').value,
          openaiApiKey: document.getElementById('openaiApiKey').value,
          enableNativeFunctionCall: document.getElementById('enableNativeFunctionCall').checked,
          limitRetrieveTestCase: document.getElementById('limitRetrieveTestCase').checked,
          ckgConstructionJarPath: document.getElementById('ckgConstructionJarPath').value,
        },
      };
      vscode.postMessage({ command: 'saveSettings', settings });
    });
  </script>
</body>
</html>
