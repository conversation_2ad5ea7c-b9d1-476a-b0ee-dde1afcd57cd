{"name": "testagent", "displayName": "TestAgent", "description": "LLM agent for test generation.", "version": "0.0.1", "engines": {"vscode": "^1.99.0"}, "categories": ["Other"], "activationEvents": ["onCommand:testagent.helloWorld", "onCommand:testagent.showGraph", "onCommand:testagent.configure", "onCommand:testagent.generateTestCases"], "main": "./out/extension.js", "contributes": {"viewsContainers": {"activitybar": [{"id": "testagent", "title": "TestAgent", "icon": "./resources/icon.png"}]}, "views": {"testagent": [{"id": "testagent.sidebar", "name": "TestAgent Sidebar", "type": "tree"}]}, "commands": [{"command": "testagent.helloWorld", "title": "Hello World"}, {"command": "testagent.showGraph", "title": "Show Knowledge Graph"}, {"command": "testagent.configure", "title": "Configure <PERSON><PERSON>s"}, {"command": "testagent.generateGraph", "title": "Generate Knowledge Graph"}, {"command": "testagent.getTestableMethods", "title": "Fetch Testable Methods"}, {"command": "testagent.showMethodCallGraph", "title": "Show Method Call Graph"}, {"command": "testagent.generateTestCases", "title": "Generate Test Cases"}, {"command": "testagent.jumpToTestableMethod", "title": "Jump to Testable Method"}, {"command": "testagent.locateInSidebar", "title": "Locate in Testable Methods"}, {"command": "testagent.showInKnowledgeGraph", "title": "Show in Knowledge Graph"}, {"command": "testagent.refreshTestableMethods", "title": "Refresh Testable Methods"}, {"command": "testagent.injectTestCase", "title": "Inject Test Case"}, {"command": "testagent.injectAllTestCases", "title": "Inject All Test Cases"}], "menus": {"view/item/context": [{"command": "testagent.generateTestCases", "when": "view == testagent.sidebar && viewItem == methodNode", "group": "navigation"}, {"command": "testagent.jumpToTestableMethod", "when": "view == testagent.sidebar && viewItem == methodNode", "group": "navigation"}, {"command": "testagent.showInKnowledgeGraph", "when": "view == testagent.sidebar && viewItem == methodNode", "group": "navigation"}, {"command": "testagent.refreshTestableMethods", "when": "view == testagent.sidebar && viewItem == testableMethodsRoot", "group": "navigation"}, {"command": "testagent.injectTestCase", "when": "view == testagent.sidebar && viewItem == methodNode", "group": "navigation"}, {"command": "testagent.injectAllTestCases", "when": "view == testagent.sidebar && viewItem == testableMethodsRoot", "group": "navigation"}], "editor/context": [{"command": "testagent.locateInSidebar", "when": "editorLangId == javascript || editorLangId == typescript || editorLangId == java", "group": "navigation"}]}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src", "test": "vscode-test"}, "devDependencies": {"@types/vscode": "^1.99.0", "@types/mocha": "^10.0.10", "@types/node": "20.x", "@typescript-eslint/eslint-plugin": "^8.28.0", "@typescript-eslint/parser": "^8.28.0", "eslint": "^9.23.0", "typescript": "^5.8.2", "@vscode/test-cli": "^0.0.10", "@vscode/test-electron": "^2.4.1"}}